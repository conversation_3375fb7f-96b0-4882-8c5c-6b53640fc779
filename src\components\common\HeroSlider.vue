<template>
  <section class="hero-slider">
    <div class="slider-container">
      <div class="slider-wrapper" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
        <div class="slide" v-for="(slide, index) in slides" :key="index">
          <div class="slide-content" :style="{ backgroundImage: `url(${slide.background})` }">
            <div class="slide-text">
              <img :src="slide.logo" :alt="slide.title" />
              <p>{{ slide.description }}</p>
              <button class="learn-more-btn" @click="handleLearnMore(slide)">{{ learnMoreText }}</button>
            </div>
          </div>
        </div>
      </div>
      <button class="slider-btn prev-btn" @click="prevSlide" :aria-label="prevButtonText">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <button class="slider-btn next-btn" @click="nextSlide" :aria-label="nextButtonText">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <div class="slider-dots">
        <span
          v-for="(slide, index) in slides"
          :key="index"
          class="dot"
          :class="{ active: index === currentSlide }"
          @click="goToSlide(index)"
          :aria-label="`跳转到第${index + 1}张幻灯片`"
        ></span>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  slides: {
    type: Array,
    required: true,
    default: () => []
  },
  autoPlay: {
    type: Boolean,
    default: true
  },
  autoPlayInterval: {
    type: Number,
    default: 5000
  },
  learnMoreText: {
    type: String,
    default: '瞭解更多 >>'
  },
  prevButtonText: {
    type: String,
    default: '上一张'
  },
  nextButtonText: {
    type: String,
    default: '下一张'
  }
})

// Emits
const emit = defineEmits(['learn-more', 'slide-change'])

// 响应式数据
const currentSlide = ref(0)
const autoPlayTimer = ref(null)

// 方法
const nextSlide = () => {
  const newSlide = (currentSlide.value + 1) % props.slides.length
  currentSlide.value = newSlide
  emit('slide-change', newSlide)
}

const prevSlide = () => {
  const newSlide = currentSlide.value === 0 ? props.slides.length - 1 : currentSlide.value - 1
  currentSlide.value = newSlide
  emit('slide-change', newSlide)
}

const goToSlide = (index) => {
  currentSlide.value = index
  emit('slide-change', index)
}

const startAutoPlay = () => {
  if (props.autoPlay && props.slides.length > 1) {
    autoPlayTimer.value = setInterval(() => {
      nextSlide()
    }, props.autoPlayInterval)
  }
}

const stopAutoPlay = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = null
  }
}

const handleLearnMore = (slide) => {
  emit('learn-more', slide)
}

// 生命周期钩子
onMounted(() => {
  startAutoPlay()
})

onBeforeUnmount(() => {
  stopAutoPlay()
})

// 暴露方法给父组件
defineExpose({
  nextSlide,
  prevSlide,
  goToSlide,
  startAutoPlay,
  stopAutoPlay
})
</script>

<style scoped>
/* 轮播图样式 */
.hero-slider {
  position: relative;
  height: 600px;
  overflow: hidden;
}

.slider-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.slider-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.slide-content {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  position: relative;
}

.slide-text {
  max-width: 500px;
  padding: 0 80px;
  color: #fff;
}

.slide-text img {
  max-width: 200px;
  margin-bottom: 30px;
}

.slide-text p {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 30px;
}

.learn-more-btn {
  padding: 12px 24px;
  background: transparent;
  color: #fff;
  border: 1px solid #fff;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.learn-more-btn:hover {
  background: #fff;
  color: #333;
}

/* 轮播图控制按钮 */
.slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-btn svg {
  width: 20px;
  height: 20px;
}

.slider-btn:hover {
  background: rgba(255, 255, 255, 0.5);
}

.prev-btn {
  left: 30px;
}

.next-btn {
  right: 30px;
}

/* 轮播图指示点 */
.slider-dots {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s;
}

.dot.active {
  background: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .slide-text {
    padding: 0 30px;
  }
  
  .slide-text img {
    max-width: 150px;
    margin-bottom: 20px;
  }
  
  .slide-text p {
    font-size: 16px;
    margin-bottom: 20px;
  }
  
  .learn-more-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
  
  .slider-btn {
    width: 40px;
    height: 40px;
  }

  .slider-btn svg {
    width: 16px;
    height: 16px;
  }
  
  .prev-btn {
    left: 15px;
  }
  
  .next-btn {
    right: 15px;
  }
}
</style>
